import Header from "@/common/header";
import React, { useCallback, useEffect, useState } from "react";
import SheetDemo from "src/components/SettingsDrawer";
import { Label, Switch, Text, View, XStack, YStack } from "tamagui";
import { useDashboardStyles } from "./Styles/DashboardStyle";
import ConsultationTabs from "./ConsultationTabs";
import { useAuth } from "~/context/AuthContext";
import axiosConfig from "~/services/axiosConfig";
// import { VoipCallTester } from "src/components/VoipCallTester";

export default function Dashboard() {
  const dashboardStyles = useDashboardStyles();
  const {
    container,
    mainStack,
    consultationTitle,
    toggleContainer,
    todayLabel,
    toggleText,
    toggleStack,
    switchStyle,
    switchThumbStyle,
  } = dashboardStyles;

  const [open, setOpen] = useState(false);
  const [isActive, setIsActive] = useState(false);
  const { user } = useAuth();

  const handlePress = useCallback(() => {
    setOpen(true);
  }, []);

  const { role, isOnline } = user || {};

  useEffect(() => {
    if (role === "provider") {
      setIsActive(!!isOnline);
    }
  }, [role, isOnline]);

  const handleStatusChange = async (newStatus: boolean) => {
    setIsActive(newStatus);
    try {
      await axiosConfig.put("/provider/online", {
        providerId: user?.id,
        status: newStatus,
      });
    } catch (error) {
      console.error("Error updating provider status", error);
    }
  };

  return (
    <View {...container}>
      <YStack {...mainStack}>
        <Header onAvatarPress={handlePress} />

        <Text {...consultationTitle}>Consultations</Text>
        <XStack {...toggleContainer}>
          <Text {...todayLabel}>TODAY</Text>

          <XStack {...toggleStack}>
            <Switch
              {...switchStyle}
              checked={isActive}
              onCheckedChange={handleStatusChange}
              backgroundColor={
                isActive ? "$switchToggleGreen" : "$inactiveToggleColor"
              }
            >
              <Switch.Thumb {...switchThumbStyle} />
            </Switch>
            <Label {...toggleText}>{isActive ? "Online" : "Offline"}</Label>
          </XStack>
        </XStack>

        <YStack flex={1}>
          <ConsultationTabs />
        </YStack>

        {open && <SheetDemo open={open} setOpen={setOpen} />}
      </YStack>
    </View>
  );
}
