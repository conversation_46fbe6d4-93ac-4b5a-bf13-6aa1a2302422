import { useEffect, useRef, useState, useCallback } from "react";
import {
  Platform,
  Vibration,
  Linking,
  AppState,
  AppStateStatus,
  NativeModules,
} from "react-native";
import VoipPushNotification from "react-native-voip-push-notification";
import Sound from "react-native-sound";
import axiosConfig from "~/services/axiosConfig";
import DeviceInfo from "react-native-device-info";
import { useConsultationRequest } from "~/context/ConsultationRequestContext";
import RNCallKeep from "react-native-callkeep";

const APP_SCHEME =
  "com.austinr47.VITALCARE://provider/dashboard?requireAuth=true";

// We don't need to grab the native CallKeep module directly anymore
// as we're using the RNCallKeep import for all CallKit interactions

/**
 * Your VoIP push payload
 */
export interface VoipNotificationPayload {
  uuid: string;
  consultationRequestId: string;
  facilityId: string;
  patientName: string;
  patientDOB: string;
  patientGender: string;
  location: string;
  caller: string;
  chief_complaint: string;
  timeLeft: number;
}

/**
 * Hook to register for VoIP pushes, show CallKit UI, handle Answer/End,
 * and enqueue your in‑app consultation request.
 */
export const useVoipNotifications = () => {
  const soundRef = useRef<Sound | null>(null);
  const stopTimerRef = useRef<NodeJS.Timeout | null>(null);
  const payloadRef = useRef<VoipNotificationPayload | null>(null);
  const [isRegistered, setIsRegistered] = useState(false);
  const [isRinging, setIsRinging] = useState(false);
  const { setRequestQueue } = useConsultationRequest();

  // --- RINGTONE LOGIC ---
  const stopRingtone = useCallback(() => {
    // Log that the function was called
    console.log("VoIP: stopRingtone called");

    const eventBody = {
      level: "INFO",
      status: "stopped",
      name: "use-voip-notifications",
      message: "stopRingtone called",
      metadata: { schema: APP_SCHEME },
    };
    axiosConfig.post("/log-event", eventBody);

    // Make sure to stop the sound
    if (soundRef.current) {
      try {
        soundRef.current.stop();
        soundRef.current.release();
        console.log("VoIP: Sound stopped and released");
      } catch (err) {
        console.error("Error stopping sound:", err);
      } finally {
        soundRef.current = null;
      }
    }

    // Clear any timers
    if (stopTimerRef.current) {
      clearTimeout(stopTimerRef.current);
      stopTimerRef.current = null;
      console.log("VoIP: Ringtone timer cleared");
    }

    // Cancel vibration - make sure this is always called
    Vibration.cancel();
    console.log("VoIP: Vibration canceled");

    // Update state
    setIsRinging(false);
  }, []);

  const playRingtone = useCallback(() => {
    const eventBody = {
      level: "INFO",
      status: "playing",
      name: "use-voip-notifications",
      message: "playRingtone called",
      metadata: { schema: APP_SCHEME },
    };
    axiosConfig.post("/log-event", eventBody);

    if (isRinging) return;
    setIsRinging(true);

    // Start vibration pattern
    Vibration.vibrate([0, 500, 200, 500], true);
    console.log("VoIP: Started vibration pattern");

    // Set a timer to stop vibration after 30 seconds regardless of other actions
    const vibrationTimer = setTimeout(() => {
      console.log("VoIP: Auto-stopping vibration after 30 seconds");
      Vibration.cancel();
    }, 30000);

    const file = Platform.OS === "ios" ? "ringtone.caf" : "ringtone.mp3";
    soundRef.current = new Sound(file, Sound.MAIN_BUNDLE, (err) => {
      if (err) {
        console.error("VoIP: Sound load error", err);
        return;
      }
      soundRef.current?.setNumberOfLoops(-1);
      soundRef.current?.setVolume(1);
      soundRef.current?.play((success) => {
        if (!success) console.error("VoIP: Playback decode error");
      });

      // Set a timer to stop the ringtone after 30 seconds
      stopTimerRef.current = setTimeout(() => {
        console.log("VoIP: Auto-stopping ringtone after 30 seconds");
        stopRingtone();
        // Clear the vibration timer if stopRingtone is called before the vibration timer fires
        clearTimeout(vibrationTimer);
      }, 30000);
    });
  }, [isRinging, stopRingtone]);

  // --- ANSWER & END HANDLERS ---
  useEffect(() => {
    // const eventBodyInit = {
    //   level: "INFO",
    //   status: "init",
    //   name: "use-voip-notifications",
    //   message: "setup answer/end listeners",
    //   metadata: { schema: APP_SCHEME },
    // };
    // axiosConfig.post("/log-event", eventBodyInit);

    const answerSub = RNCallKeep.addEventListener(
      "answerCall",
      ({ callUUID }: { callUUID: string }) => {
        const eventBody = {
          level: "INFO",
          status: "answered",
          name: "use-voip-notifications",
          message: `answerCall handler for ${callUUID}`,
          metadata: {
            schema: APP_SCHEME,
            callUUID,
            payload: payloadRef?.current,
          },
        };
        axiosConfig.post("/log-event", eventBody);

        // 1) Tell CallKit the call is now active
        RNCallKeep.setCurrentCallActive(callUUID);

        // Store the UUID for later reference
        console.log(`Storing call UUID for later: ${callUUID}`);

        // 2) Check if app is already in foreground
        const currentState = AppState.currentState;
        // Only open URL if app is not already active
        if (currentState !== "active") {
          console.log("App not active, opening URL scheme");
          Linking.openURL(APP_SCHEME).catch(() => {});
        } else {
          // If app is already active, just end the call without any redirects
          console.log(
            "App already active, just ending the call without redirecting"
          );
          setTimeout(() => {
            console.log(`Ending CallKit call with UUID: ${callUUID}`);
            RNCallKeep.endCall(callUUID);

            // Also try to use the native module for more reliable call ending
            const { RingtoneModule } = NativeModules;
            if (
              RingtoneModule &&
              typeof RingtoneModule.directEndAllCalls === "function"
            ) {
              console.log("Using native module to end all calls");
              RingtoneModule.directEndAllCalls()
                .then((result: boolean) => {
                  console.log("Direct end all calls result:", result);
                })
                .catch((error: any) => {
                  console.log("Error using direct method to end calls:", error);
                });
            }
          }, 1000);
        }

        // 3) Enqueue payload & stop ringtone
        const d = payloadRef.current;
        if (d) {
          setRequestQueue(() => [
            { ...d, timeLeft: 30 } as VoipNotificationPayload,
          ]);
        }
        stopRingtone();
      }
    );

    const endSub = RNCallKeep.addEventListener("endCall", ({ callUUID }) => {
      const eventBody = {
        level: "INFO",
        status: "ended",
        name: "use-voip-notifications",
        message: `endCall handler for ${callUUID}`,
        metadata: { schema: APP_SCHEME, callUUID },
      };
      axiosConfig.post("/log-event", eventBody);
      stopRingtone();
    });

    return () => {
      answerSub.remove();
      endSub.remove();
    };
  }, [setRequestQueue, stopRingtone]);

  // --- HANDLE VoIP PUSH ---
  useEffect(() => {
    if (Platform.OS !== "ios") return;

    // const eventBodyInit = {
    //   level: "INFO",
    //   status: "init",
    //   name: "use-voip-notifications",
    //   message: "setup VoIP notification listener",
    //   metadata: { schema: APP_SCHEME },
    // };
    // axiosConfig.post("/log-event", eventBodyInit);

    const onNotification = (payload: any) => {
      // this is the one that sends the data to the modal
      const eventBody = {
        level: "INFO",
        status: "received",
        name: "use-voip-notifications",
        message: `VoIP push received: ${payload.uuid}`,
        metadata: { schema: APP_SCHEME, uuid: payload.uuid, payload },
      };
      axiosConfig.post("/log-event", eventBody);

      payloadRef.current = payload as VoipNotificationPayload;

      // Check if app is in foreground
      const currentState = AppState.currentState;

      // Always add to request queue regardless of app state
      const d = payload as VoipNotificationPayload;
      if (d) {
        setRequestQueue((prevQueue) => {
          // Check if this request is already in the queue to avoid duplicates
          const exists = prevQueue.some(
            (req) => req.consultationRequestId === d.consultationRequestId
          );
          if (exists) {
            return prevQueue;
          }
          return [{ ...d, timeLeft: 30 } as VoipNotificationPayload];
        });
      }

      // Only play ringtone if app is not in foreground
      if (currentState !== "active") {
        // Play ringtone
        playRingtone();

        // Note: CallKit UI is now handled by the native code in AppDelegate.mm
        // The native code checks if the app is in foreground and only shows CallKit UI if it's not
      }

      if (payload.uuid) {
        VoipPushNotification.onVoipNotificationCompleted(payload.uuid);
      }
    };

    VoipPushNotification.addEventListener("notification", onNotification);
    VoipPushNotification.addEventListener("didLoadWithEvents", (events) => {
      events.forEach((evt: any) => {
        const d = evt.data as VoipNotificationPayload;
        if (d.uuid) onNotification(d);
      });
    });

    VoipPushNotification.registerVoipToken();

    return () => {
      VoipPushNotification.removeEventListener("notification");
      VoipPushNotification.removeEventListener("didLoadWithEvents");
    };
  }, [playRingtone]);

  // --- REGISTER VoIP TOKEN ON SERVER ---
  const registerTokenOnServer = useCallback(async (token: string) => {
    // Log the event
    const eventBody = {
      level: "INFO",
      status: "token_registered",
      name: "use-voip-notifications",
      message: `VoIP token registered: ${token}`,
      metadata: { schema: APP_SCHEME, token },
    };
    axiosConfig.post("/log-event", eventBody);

    try {
      console.log("voip-token--------------> ", token);
      await axiosConfig.post("/user/voip-token", {
        voipToken: token,
        deviceType: DeviceInfo.getSystemName(),
      });
      setIsRegistered(true);
    } catch (err) {
      console.error("VoIP: Token registration failed", err);
    }
  }, []);

  useEffect(() => {
    if (Platform.OS !== "ios") return;

    const onRegister = (token: string) => {
      registerTokenOnServer(token);
    };
    VoipPushNotification.addEventListener("register", onRegister);

    return () => {
      VoipPushNotification.removeEventListener("register");
    };
  }, [registerTokenOnServer]);

  // Add AppState listener for logging purposes only
  useEffect(() => {
    if (Platform.OS !== "ios") return;

    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      console.log(`App state changed to: ${nextAppState}`);

      // When app becomes active, just log - don't automatically end calls
      if (nextAppState === "active") {
        console.log(
          "App became active - NOT automatically ending calls to preserve ongoing sessions"
        );
        // Note: Removed automatic call ending logic to prevent calls from being terminated
        // when user returns to app from background. Calls should only end when explicitly
        // ended by user action or when the actual call/session ends.
      }
    };

    // Subscribe to app state changes
    const appStateSubscription = AppState.addEventListener(
      "change",
      handleAppStateChange
    );

    return () => {
      appStateSubscription.remove();
    };
  }, []);

  return {
    isRegistered,
    isRinging,
  };
};
